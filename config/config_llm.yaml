preferred_endpoint: openai

endpoints:
  inception:
    api_key_env: INCEPTION_API_KEY
    api_endpoint_env: INCEPTION_ENDPOINT
    llm_type: inception
    models:
      high: mercury-small
      low: mercury-small

  openai:
    api_key_env: OPENAI_API_KEY
    api_endpoint_env: OPENAI_ENDPOINT
    llm_type: openai
    models:
      high: gpt-4.1
      low: gpt-4.1-mini

  anthropic:
    api_key_env: ANTHROPIC_API_KEY
    llm_type: anthropic
    models:
      high: claude-3-7-sonnet-latest
      low: claude-3-5-haiku-latest

  gemini:
    api_key_env: GEMINI_API_KEY
    llm_type: gemini
    models:
      high: gemini-2.0-flash
      low: gemini-2.0-flash

  azure_openai:
    api_key_env: AZURE_OPENAI_API_KEY
    api_endpoint_env: AZURE_OPENAI_ENDPOINT
    api_version_env: "2024-12-01-preview"
    llm_type: azure_openai
    models:
      high: gpt-4.1
      low: gpt-4.1-mini

  llama_azure:
    api_key_env: LLAMA_AZURE_API_KEY
    api_endpoint_env: LLAMA_AZURE_ENDPOINT
    api_version_env: "2024-12-01-preview"
    llm_type: llama_azure
    models:
      high: llama-2-70b
      low: llama-2-13b

  deepseek_azure:
    api_key_env: DEEPSEEK_AZURE_API_KEY
    api_endpoint_env: DEEPSEEK_AZURE_ENDPOINT
    api_version_env: "2024-12-01-preview"
    llm_type: deepseek_azure
    models:
      high: deepseek-coder-33b
      low: deepseek-coder-7b

  snowflake:
    api_key_env: SNOWFLAKE_PAT
    api_endpoint_env: SNOWFLAKE_ACCOUNT_URL
    api_version_env: "2024-12-01"
    llm_type: snowflake
    models:
      high: claude-3-5-sonnet
      low: llama3.1-8b

  huggingface:
    api_key_env: HF_TOKEN
    llm_type: huggingface
    models:
      high: Qwen/Qwen2.5-72B-Instruct
      low: Qwen/Qwen2.5-Coder-7B-Instruct

  ollama:
    api_endpoint_env: OLLAMA_URL
    llm_type: ollama
    models:
      high: qwen3:0.6b
      low: qwen3:0.6b
/* Page-specific styles for chat pages */
body {
  height: 100vh;
  overflow: hidden;
}

.app-container {
  display: flex;
  height: 100vh;
}

/* Chat header styles for login */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.chat-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.login-button, .logout-button {
  padding: 6px 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
}

.login-button:hover, .logout-button:hover {
  background-color: var(--bg-hover);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
  color: var(--text-secondary);
}

.provider-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}

.provider-icon.google {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBmaWxsPSIjNDI4NUY0IiBkPSJNMjIuNTYgMTIuMjVjMC0uNzgtLjA3LTEuNTMtLjItMi4yNUgxMnY0LjI2aDUuOTJjLS4yNiAxLjM3LTEuMDQgMi41My0yLjIxIDMuMzF2Mi43N2gzLjU3YzIuMDgtMS45MiAzLjI4LTQuNzQgMy4yOC04LjA5eiIvPjxwYXRoIGZpbGw9IiMzNEE4NTMiIGQ9Ik0xMiAyM2MyLjk3IDAgNS40Ni0uOTggNy4yOC0yLjY2bC0zLjU3LTIuNzdjLS45OC42Ni0yLjIzIDEuMDYtMy43MSAxLjA2LTIuODYgMC01LjI5LTEuOTMtNi4xNi00LjUzSDIuMTh2Mi44NEM0IDIwLjUzIDcuNyAyMyAxMiAyM3oiLz48cGF0aCBmaWxsPSIjRkJCQzA1IiBkPSJNNS44NCAxNC4wOWMtLjIyLS42Ni0uMzUtMS4zNi0uMzUtMi4wOXMuMTMtMS40My4zNS0yLjA5VjcuMDdIMi4xOEMxLjQzIDguNTUgMSAxMC4yMiAxIDEyczQzMyAzLjQ1IDEuMTggNC45M2wyLjg1LTIuMjIuODEtLjYyeiIvPjxwYXRoIGZpbGw9IiNFQTQzMzUiIGQ9Ik0xMiA1LjM4YzEuNjIgMCAzLjA2LjU2IDQuMjEgMS42NGwzLjE1LTMuMTVDMTcuNDUgMi4wOSAxNC45NyAxIDEyIDEgNy43IDEgNCAwIDMuNDcgMi4xOCA3LjA3bDMuNjYgMi44NGMuODctMi42IDMuMy00LjUzIDYuMTYtNC41M3oiLz48L3N2Zz4=');
}

.provider-icon.facebook {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBmaWxsPSIjMTg3N0YyIiBkPSJNMjQgMTIuMDczYzAtNi42MjctNS4zNzMtMTItMTItMTJzLTEyIDUuMzczLTEyIDEyYzAgNS45OSA0LjM4OCAxMC45NTQgMTAuMTI1IDExLjg1NHYtOC4zODVINy4wNzh2LTMuNDdoMy4wNDdWOS40M2MwLTMuMDA3IDEuNzkyLTQuNjY5IDQuNTMzLTQuNjY5IDEuMzEyIDAgMi42ODYuMjM1IDIuNjg2LjIzNXYyLjk1M0gxNS44M2MtMS40OTEgMC0xLjk1Ni45MjUtMS45NTYgMS44NzR2Mi4yNWgzLjMyOGwtLjUzMiAzLjQ3aC0yLjc5NnY4LjM4NUMxOS42MTIgMjMuMDI3IDI0IDE4LjA2MiAyNCAxMi4wNzN6Ii8+PC9zdmc+');
}

.provider-icon.microsoft {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBmaWxsPSIjRjI1MDIyIiBkPSJNMTEuNCAxMS40SDF2MEgxMS40VjExLjR6Ii8+PHBhdGggZmlsbD0iIzdGQkEwMCIgZD0iTTI0IDExLjRIMTIuNnYwSDI0VjExLjR6Ii8+PHBhdGggZmlsbD0iIzAwQTRFRiIgZD0iTTExLjQgMjRIMXYwSDExLjRWMjR6Ii8+PHBhdGggZmlsbD0iI0ZGQjkwMCIgZD0iTTI0IDI0SDEyLjZ2MEgyNFYyNHoiLz48L3N2Zz4=');
}

.provider-icon.github {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBmaWxsPSIjMzMzIiBkPSJNMTIgMGMtNi42MjYgMC0xMiA1LjM3My0xMiAxMiAwIDUuMzAyIDMuNDM4IDkuOCA4LjIwNyAxMS4zODcuNTk5LjExMS43OTMtLjI2MS43OTMtLjU3N3YtMi4yMzRjLTMuMzM4LjcyNi00LjAzMy0xLjQxNi00LjAzMy0xLjQxNi0uNTQ2LTEuMzg3LTEuMzMzLTEuNzU2LTEuMzMzLTEuNzU2LTEuMDg5LS43NDUuMDgzLS43MjkuMDgzLS43MjkgMS4yMDUuMDg0IDEuODM5IDEuMjM3IDEuODM5IDEuMjM3IDEuMDcgMS44MzQgMi44MDcgMS4zMDQgMy40OTIuOTk3LjEwNy0uNzc1LjQxOC0xLjMwNS43NjItMS42MDQtMi42NjUtLjMwNS01LjQ2Ny0xLjMzNC01LjQ2Ny01LjkzMSAwLTEuMzExLjQ2OS0yLjM4MSAxLjIzNi0zLjIyMS0uMTI0LS4zMDMtLjUzNS0xLjUyNC4xMTctMy4xNzYgMCAwIDEuMDA4LS4zMjIgMy4zMDEgMS4yMy45NTctLjI2NiAxLjk4My0uMzk5IDMuMDAzLS40MDQgMS4wMi4wMDUgMi4wNDcuMTM4IDMuMDA2LjQwNCAyLjI5MS0xLjU1MiAzLjI5Ny0xLjIzIDMuMjk3LTEuMjMuNjUzIDEuNjUzLjI0MiAyLjg3NC4xMTggMy4xNzYuNzcuODQgMS4yMzUgMS45MTEgMS4yMzUgMy4yMjEgMCA0LjYwOS0yLjgwNyA1LjYyNC01LjQ3OSA1LjkyMS40My4zNzIuODIzIDEuMTAyLjgyMyAyLjIyMnYzLjI5M2MwIC4zMTkuMTkyLjY5NC44MDEuNTc2IDQuNzY1LTEuNTg5IDguMTk5LTYuMDg2IDguMTk5LTExLjM4NiAwLTYuNjI3LTUuMzczLTEyLTEyLTEyeiIvPjwvc3ZnPg==');
}


/* OAuth Popup Styles */
.oauth-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.oauth-popup {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 400px;
  overflow: hidden;
}

.oauth-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.oauth-popup-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.oauth-popup-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: var(--transition);
}

.oauth-popup-close:hover {
  background-color: var(--bg-hover);
}

.oauth-popup-content {
  padding: 20px;
}

.oauth-popup-content p {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  text-align: center;
}

.oauth-providers {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.oauth-provider-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.oauth-provider-button:hover {
  background-color: var(--bg-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.oauth-icon {
  width: 20px;
  height: 20px;
}

.oauth-provider-button.google:hover {
  border-color: #4285F4;
}

.oauth-provider-button.facebook:hover {
  border-color: #1877F2;
}

.oauth-provider-button.microsoft:hover {
  border-color: #F25022;
}

/* Site selector dropdown */
.site-dropdown {
  position: absolute;
  left: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  padding: 8px;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  z-index: 10;
}

/* Position below in centered input, above in regular input */
.centered-input-container .site-dropdown {
  top: 100%;
  margin-top: 8px;
}

.chat-input-container .site-dropdown {
  bottom: 100%;
  margin-bottom: 8px;
}

.site-dropdown.show {
  display: block;
}

.site-dropdown-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
}

.site-dropdown-item:hover {
  background-color: var(--hover-bg);
}

.site-dropdown-item.selected {
  background-color: var(--primary-color);
  color: white;
}

.site-dropdown-header {
  font-size: 12px;
  color: var(--text-secondary);
  padding: 4px 12px;
  font-weight: 600;
}

/* Mode selector - positioned at bottom */
.input-site-selector {
  position: relative;
  margin-right: 8px;
}

.site-selector-icon {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.site-selector-icon:hover {
  background-color: var(--hover-bg);
}

.site-selector-icon svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.input-mode-selector {
  position: relative;
  margin-right: 8px;
}

.mode-selector-icon {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.mode-selector-icon:hover {
  background-color: var(--hover-bg);
}

.mode-selector-icon svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.mode-dropdown {
  position: absolute;
  left: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  padding: 8px;
  min-width: 150px;
  display: none;
  z-index: 10;
}

/* Position below in centered input, above in regular input */
.centered-input-container .mode-dropdown {
  top: 100%;
  margin-top: 8px;
}

.chat-input-container .mode-dropdown {
  bottom: 100%;
  margin-bottom: 8px;
}

.mode-dropdown.show {
  display: block;
}

.mode-dropdown-header {
  padding: 8px 12px;
  font-weight: 600;
  font-size: 13px;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 4px;
}

.mode-dropdown-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
}

.mode-dropdown-item:hover {
  background-color: var(--hover-bg);
}

.mode-dropdown-item.selected {
  background-color: var(--primary-color);
  color: white;
}

/* Messages container with max-width */
.messages-container {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  padding: 24px;
}

.message {
  display: flex;
  gap: 16px;
}

.message-content {
  padding-top: 4px;
}

/* Input Area */
.chat-input-container {
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  padding: 16px 0;
}

.chat-input-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.chat-input-box {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.chat-input-box:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(94, 94, 255, 0.1);
}

.chat-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
  outline: none;
  max-height: 200px;
  overflow-y: auto;
  font-family: inherit;
}

.chat-input::placeholder {
  color: #999;
}

.send-button {
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.send-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.send-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.send-button svg {
  width: 18px;
  height: 18px;
}

/* Search Results Styling */
.search-results {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.search-result-item {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  transition: var(--transition);
}

.search-result-item:hover {
  background-color: var(--hover-bg);
}

.result-title {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  display: block;
  margin-bottom: 4px;
}

.result-title:hover {
  text-decoration: underline;
}

.result-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

/* Centered Input Styles */
.centered-input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 24px;
}

/* Input box row styles for centered input */
.input-box-top-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-box-bottom-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.centered-input-wrapper {
  width: 100%;
  max-width: 800px;
}

.site-selector-wrapper {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.site-selector-wrapper label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.site-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.site-select:hover {
  border-color: var(--primary-color);
}

.site-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(94, 94, 255, 0.1);
}

.centered-input-box {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 0;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.centered-input-box:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(94, 94, 255, 0.1), var(--shadow-md);
}

.centered-chat-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 18px;
  line-height: 1.6;
  resize: none;
  outline: none;
  min-height: 60px;
  max-height: 200px;
  overflow-y: auto;
  font-family: inherit;
  padding: 4px 0;
}

.centered-chat-input::placeholder {
  color: #999;
}

.centered-send-button {
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.centered-send-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.centered-send-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.centered-send-button svg {
  width: 18px;
  height: 18px;
}

/* Responsive overrides */
@media (max-width: 768px) {
  .messages-container,
  .chat-input-wrapper {
    padding: 16px;
  }
}

/* Add Site Modal */
.modal {
  display: none; 
  position: fixed; 
  z-index: 1001; 
  left: 0;
  top: 0;
  width: 100%; 
  height: 100%; 
  overflow: auto; 
  background-color: rgba(0,0,0,0.4); 
}

.modal-content {
  background-color: #fefefe;
  margin: 15% auto; 
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
  max-width: 500px;
  border-radius: 8px;
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

#new-site-form-url label, #new-site-form-dir label {
  display: block;
  margin-top: 10px;
}

#new-site-form-url input, #new-site-form-dir input {
  width: 100%;
  padding: 8px;
  margin-top: 5px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

#new-site-form-url button, #new-site-form-dir button {
  margin-top: 15px;
  padding: 10px 15px;
  border: none;
  background-color: var(--primary-color);
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;                /* makes it stretch full width */
  padding: 0 0px;            /* match modal-content’s padding */
  border-bottom: 1px solid #ccc; /* optional, like a tab bar */
  box-sizing: border-box;     /* ensure padding doesn’t break width */
}


.tab-container button {
  background-color: inherit;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  transition: 0.3s;
}

.tab-container button:hover {
  background-color: #ddd;
}

.tab-container button.active {
  background-color: #ccc;
}

.tab-content {
  display: none;
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-top: none;
}

.delete-site-btn {
  background: none;
  padding: 5px;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 16px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.site-list-item:hover .delete-site-btn {
  opacity: 1;
}

.crawler-link {
  font-size: 0.85em;
  color: #0073e6;
  text-decoration: none;
  margin-left: 1rem;
}
.crawler-link:hover {
  text-decoration: underline;
}
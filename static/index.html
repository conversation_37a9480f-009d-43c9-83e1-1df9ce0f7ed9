<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NLWeb Chat</title>
  <link rel="icon" type="image/png" href="/static/favicon.png">
  <link rel="stylesheet" href="/static/common-chat-styles.css">
  <link rel="stylesheet" href="/static/chat-page-styles.css">
  <link rel="stylesheet" href="/static/right-sidebar.css">
</head>
<body>
  <div class="app-container">
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
    </button>
    
    <!-- Sidebar Toggle Button (outside sidebar) -->
    <button class="sidebar-toggle" id="sidebar-toggle" title="Toggle sidebar">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
    </button>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
      
      <div class="sidebar-header">
        <!-- Conversations header with new button -->
        <div class="conversations-header">
          <h2 class="conversations-title">Conversations</h2>
          <button class="new-chat-icon-btn" id="new-chat-btn" title="New chat">
            <span class="plus-icon">+</span>
          </button>
        </div>
      </div>
      
      <!-- Conversations list -->
      <div class="conversations-list" id="conversations-list">
        <!-- Conversation items will be dynamically added here -->
      </div>
    </aside>

    <!-- Main Chat Area -->
    <main class="main-content">
      <header class="chat-header">
        <div class="chat-header-left">
          <h1 class="chat-title">New chat</h1>
          <span class="chat-site-info" id="chat-site-info">Asking all</span>
        </div>
        <div class="chat-header-right">
          <button class="login-button" id="loginBtn">Login</button>
          <div class="user-info" id="userInfo" style="display: none;">
            <span class="provider-icon" id="providerIcon"></span>
            <span class="user-name" id="userName"></span>
            <button class="logout-button" id="logoutBtn">Logout</button>
          </div>
        </div>
      </header>

      <div class="chat-messages" id="chat-messages">
        <div class="messages-container" id="messages-container">
          <!-- Messages will be dynamically added here -->
        </div>
      </div>

      <div class="chat-input-container">
        <div class="chat-input-wrapper">
          <div class="chat-input-box">
            <div class="input-mode-selector">
              <button class="mode-selector-icon" id="mode-selector-icon" title="Select mode">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="9" y1="9" x2="15" y2="9"></line>
                  <line x1="9" y1="12" x2="15" y2="12"></line>
                  <line x1="9" y1="15" x2="11" y2="15"></line>
                </svg>
              </button>
              <div class="mode-dropdown" id="mode-dropdown">
                <div class="mode-dropdown-header">Select mode</div>
                <div class="mode-dropdown-item" data-mode="list">List</div>
                <div class="mode-dropdown-item" data-mode="summarize">Summarize</div>
                <div class="mode-dropdown-item" data-mode="generate">Generate</div>
              </div>
            </div>
            <textarea 
              class="chat-input" 
              id="chat-input"
              placeholder="Send a message..."
              rows="1"
            ></textarea>
            <button class="send-button" id="send-button">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg>
              Send
            </button>
          </div>
        </div>
      </div>
    </main>
  <!-- Right Sidebar -->
    <aside class="right-sidebar" id="right-sidebar" aria-label="Right sidebar" aria-hidden="true">
      <div class="right-sidebar-content">
        <div class="right-sidebar-header">
          <h2 class="right-sidebar-title">Sites</h2>
          <button class="new-site-btn" id="new-site-btn" title="Add new site">
            <span class="plus-icon">+</span>
          </button>
        </div>
        <div class="right-sidebar-section">
          <h3>Configured Sites</h3>
          <div id="right-sidebar-site-list" class="site-list-container">
            <!-- Site list will be dynamically added here -->
          </div>
        </div>
      </div>
    </aside>

    <!-- Right Sidebar Toggle Button -->
    <button class="right-sidebar-toggle" id="right-sidebar-toggle" 
            title="Toggle right sidebar" 
            aria-expanded="false" 
            aria-controls="right-sidebar">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
      <span class="sr-only">Toggle right sidebar</span>
    </button>    
  </div>

  <!-- Add Site Modal -->
  <div id="new-site-modal" class="modal">
    <div class="modal-content">
      <span class="close" id="cancel-add-site">&times;</span>
      <h2>Add a New Site</h2>
      <div class="tab-container">
        <div class="tab-buttons">
          <button class="tab-link active" onclick="openTab(event, 'url-tab')">From URL</button>
          <button class="tab-link" onclick="openTab(event, 'dir-tab')">From Directory</button>
        </div>
<a id="crawler-link" href="#" target="_blank" class="crawler-link">Configure Crawler</a>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const hostname = window.location.hostname; // e.g. "myapp.example.com" or "localhost"
    let crawlerUrl;

    if (hostname === "localhost" || hostname === "0.0.0.0") {
      // Local dev case
      crawlerUrl = `http://${hostname}:5000/`;
    } else {
      // Production/staging case
      const baseDomain = hostname.split('.').slice(-2).join('.'); 
      // e.g. "example.com" from "app.example.com"
      crawlerUrl = `https://crawler.nlweb.${baseDomain}`;
    }

    // Apply to link
    const link = document.getElementById("crawler-link");
    if (link) {
      link.href = crawlerUrl;
    }
  });
</script>     
      </div>
      <div id="url-tab" class="tab-content" style="display: block;">
        <form id="new-site-form-url">
          <label for="new-site-name-url">Name:</label>
          <input type="text" id="new-site-name-url" name="name" required>
          <label for="new-site-url">Url (only RSS supported for now):</label>
          <input type="url" id="new-site-url" name="url" required>
          <button type="submit" id="add-site-btn-url">Add Site</button>
        </form>
      </div>
      <div id="dir-tab" class="tab-content">
        <form id="new-site-form-dir">
          <label for="new-site-name-dir">Name:</label>
          <input type="text" id="new-site-name-dir" name="name" required>
          <label for="new-site-dir">Directory Path:</label>
          <input type="text" id="new-site-dir" name="directory" required>
          <button type="submit" id="add-site-btn-dir">Add Site</button>
        </form>
      </div>
    </div>
  </div>

  <!-- OAuth Login Popup -->
  <div class="oauth-popup-overlay" id="oauthPopupOverlay" style="display: none;">
    <div class="oauth-popup">
      <div class="oauth-popup-header">
        <h2>Login to NLWeb</h2>
        <button class="oauth-popup-close">&times;</button>
      </div>
      <div class="oauth-popup-content">
        <p>Choose your login provider:</p>
        <div class="oauth-providers">
          <button class="oauth-provider-button google" id="googleLoginBtn">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
          </button>
          <button class="oauth-provider-button facebook" id="facebookLoginBtn">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
            Continue with Facebook
          </button>
          <button class="oauth-provider-button microsoft" id="microsoftLoginBtn">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="#F25022" d="M11.4 11.4H1V1h10.4v10.4z"/>
              <path fill="#7FBA00" d="M23 11.4H12.6V1H23v10.4z"/>
              <path fill="#00A4EF" d="M11.4 23H1V12.6h10.4V23z"/>
              <path fill="#FFB900" d="M23 23H12.6V12.6H23V23z"/>
            </svg>
            Continue with Microsoft
          </button>
          <button class="oauth-provider-button github" id="githubLoginBtn">
            <svg class="oauth-icon" viewBox="0 0 24 24">
              <path fill="#333" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            Continue with GitHub
          </button>
        </div>
      </div>
    </div>
  </div>

  <script type="module" src="/static/fp-chat-interface.js"></script>
  <script src="/static/oauth-login.js"></script>
  <script src="/static/right-sidebar.js"></script>  
  <script>
    function openTab(evt, tabName) {
      var i, tabcontent, tablinks;
      tabcontent = document.getElementsByClassName("tab-content");
      for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
      }
      tablinks = document.getElementsByClassName("tab-link");
      for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
      }
      document.getElementById(tabName).style.display = "block";
      evt.currentTarget.className += " active";
    }
  </script>
</body>
</html>